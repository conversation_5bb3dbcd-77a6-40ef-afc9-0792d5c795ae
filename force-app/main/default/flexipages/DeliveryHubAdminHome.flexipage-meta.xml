<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>decorate</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>richTextValue</name>
                    <value>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 24px; color: rgb(68, 68, 68);&quot;&gt;Welcome to the &lt;/span&gt;&lt;strong style=&quot;font-size: 24px; color: rgb(68, 68, 68);&quot;&gt;Delivery Hub Admin &lt;/strong&gt;&lt;span style=&quot;font-size: 24px; color: rgb(68, 68, 68);&quot;&gt;Lightning App!&lt;/span&gt;&lt;/p&gt;</value>
                </componentInstanceProperties>
                <componentName>flexipage:richText</componentName>
                <identifier>flexipage_richText</identifier>
            </componentInstance>
        </itemInstances>
        <name>top</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>bottomLeft</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>bottomRight</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Home</masterLabel>
    <template>
        <name>home:desktopTemplate</name>
    </template>
    <type>HomePage</type>
</FlexiPage>

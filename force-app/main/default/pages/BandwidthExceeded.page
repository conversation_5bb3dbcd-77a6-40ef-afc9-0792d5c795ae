<apex:page showHeader="false" title="{!$Label.site.bandwidth_limit_exceeded}">
  <apex:composition template="StdExceptionTemplate">
    <apex:define name="icon">
      <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/warning.png')}"/>
    </apex:define>
    <apex:define name="error">
      <apex:outputText styleClass="title" value="{!$Label.site.limit_exceeded}"/>
      <br/>
      <br/>
      <apex:outputText value="{!$Label.site.sorry_for_inconvenience}"/>
    </apex:define>
  </apex:composition>
</apex:page>
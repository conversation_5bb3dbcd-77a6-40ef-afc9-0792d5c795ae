<apex:page showHeader="false" title="{!$Label.site.site_under_construction}">
  <apex:composition template="StdExceptionTemplate">
    <apex:define name="icon">
      <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/tools.png')}"/>
    </apex:define>
    <apex:define name="error">
      <apex:outputText escape="false" styleClass="title" value="{!$Label.site.under_construction}">
        <apex:param value="{!$Site.BaseUrl}"/>
      </apex:outputText>
      <br/>
      <br/>
      <apex:outputText value="{!$Label.site.stay_tuned}"/>
    </apex:define>
  </apex:composition>
</apex:page>
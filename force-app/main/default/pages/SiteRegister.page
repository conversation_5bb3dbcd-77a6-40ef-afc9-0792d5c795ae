<apex:page id="Registration" showHeader="false" controller="SiteRegisterController" title="{!$Label.site.register}">
  <apex:composition template="{!$Site.Template}">
    <apex:define name="body">  
      <center>
        <apex:panelGrid bgcolor="white" columns="1"> 
          <br/>
          <br/>
          <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="1" styleClass="topPanelContainer"> 
            <br/>
            <apex:outputPanel layout="block" styleClass="topPanel">
              <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="2"> 
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/clock.png')}"/>
                <apex:panelGroup >
                  <br/>
                  <apex:outputText styleClass="title" value="{!$Label.site.user_registration}"/>
                  <br/>
                  <apex:form id="theForm" forceSSL="true">
                    <apex:pageMessages id="error"/>
                    <apex:panelGrid columns="2" style="margin-top:1em;">
                      <apex:outputLabel value="{!$Label.site.username}" for="username"/>
                      <apex:inputText required="true" id="username" value="{!username}"/>
                      <apex:outputLabel value="{!$Label.site.community_nickname}" for="communityNickname"/>
                      <apex:inputText required="true" id="communityNickname" value="{!communityNickname}"/>
                      <apex:outputLabel value="{!$Label.site.email}" for="email"/>
                      <apex:inputText required="true" id="email" value="{!email}"/>
                      <apex:outputLabel value="{!$Label.site.password}" for="password"/>
                      <apex:inputSecret id="password" value="{!password}"/>
                      <apex:outputLabel value="{!$Label.site.confirm_password}" for="confirmPassword"/>
                      <apex:inputSecret id="confirmPassword" value="{!confirmPassword}"/>
                      <apex:outputText value=""/>
                      <apex:commandButton action="{!registerUser}" value="{!$Label.site.submit}" id="submit"/>
                    </apex:panelGrid> 
                    </apex:form>                  
                  <br/>
                </apex:panelGroup>
              </apex:panelGrid> 
             </apex:outputPanel>
            <c:SitePoweredBy />
          </apex:panelGrid> 
       </apex:panelGrid>
      </center>
      <br/>
    </apex:define>
  </apex:composition>
</apex:page>
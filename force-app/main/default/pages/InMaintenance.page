<apex:page showHeader="false" title="{!$Label.site.in_maintenance}" cache="false">
  <apex:composition template="StdExceptionTemplate">
    <apex:define name="icon">
      <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/warning.png')}"/>
    </apex:define>
    <apex:define name="error">
      <apex:outputText escape="false" styleClass="title" value="{!$Label.site.down_for_maintenance}">
        <apex:param value="{!$Site.BaseUrl}"/>
      </apex:outputText>
      <br/>
      <br/>
      <apex:outputText value="{!$Label.site.sorry_for_inconvenience_back_shortly}"/>
   </apex:define>
  </apex:composition>
</apex:page>
<apex:page showHeader="false" title="{!$Label.site.registration_confirmation}">
  <apex:composition template="{!$Site.Template}">
    <apex:define name="body">  
      <center>
        <apex:panelGrid bgcolor="white" columns="1" style="align: center;"> 
          <br/>
          <br/>
          <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="1" style="topPanel"> 
            <br/>
            <apex:outputPanel layout="block" styleClass="topPanelContainer">
              <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="2"> 
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/clock.png')}"/>
                <apex:panelGroup >
                  <br/>
                  <apex:outputText styleClass="title" value="{!$Label.site.thank_you_for_registering}"/>
                  <br/>
                  <br/>
                  <apex:outputLink value="{!$Page.SiteLogin}" rendered="{!$Site.LoginEnabled}">{!$Label.site.go_to_login_page}</apex:outputLink>
                  <br/>
                </apex:panelGroup>
              </apex:panelGrid> 
            </apex:outputPanel>
            <c:SitePoweredBy />
          </apex:panelGrid> 
       </apex:panelGrid>
      </center>
      <br/>
    </apex:define>
  </apex:composition>
</apex:page>
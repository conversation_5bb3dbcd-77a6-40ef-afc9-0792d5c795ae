<apex:page id="loginPage" showHeader="false" title="{!$Label.site.site_login}">
  <apex:composition template="{!$Site.Template}">
    <apex:define name="body">  
      <center>
        <apex:panelGrid bgcolor="white" columns="1"> 
          <br/>
          <br/>
          <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="1" styleClass="topPanelContainer"> 
            <br/>
            <apex:outputPanel layout="block" styleClass="topPanel">
              <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="2"> 
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/clock.png')}"/>
                <apex:panelGroup >
                  <br/>
                  <apex:outputText styleClass="title" value="{!$Label.site.login}"/>
                  <br/>
                  <c:SiteLogin id="siteLogin"/>
                  <br/>
                </apex:panelGroup>
              </apex:panelGrid> 
             </apex:outputPanel>
            <c:SitePoweredBy />
          </apex:panelGrid> 
       </apex:panelGrid>
      </center>
      <br/>
    </apex:define>
  </apex:composition>
</apex:page>
<apex:page id="forgotPassword" showHeader="false" controller="ForgotPasswordController" title="{!$Label.site.forgot_password}">
 <apex:composition template="{!$Site.Template}">
    <apex:define name="body">
      <center>
        <apex:panelGrid bgcolor="white" columns="1"> 
          <br/>
          <br/>
          <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="1" styleClass="topPanelContainer"> 
            <br/>
            <apex:outputPanel layout="block" styleClass="topPanel">
              <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="2"> 
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/clock.png')}"/>
                <apex:panelGroup >
                  <br/>
                  <apex:outputText styleClass="title" value="{!$Label.site.enter_password}"/>
                  <br/>
                  <apex:form id="theForm">
                    <apex:pageMessages id="error"/>
                    <apex:panelGrid columns="3" style="margin-top:1em;">
                      <apex:outputLabel value="{!$Label.site.username}" for="username"/>
                      <apex:inputText required="true" id="username" value="{!username}"/>
                      <apex:commandButton id="submit" value="{!$Label.site.submit}" action="{!forgotPassword}"/>
                    </apex:panelGrid> 
                    </apex:form>                  
                  <br/>
                </apex:panelGroup>
              </apex:panelGrid> 
             </apex:outputPanel>
            <c:SitePoweredBy />
          </apex:panelGrid> 
       </apex:panelGrid>
      </center>
      <br/>
    </apex:define>
  </apex:composition>
</apex:page>
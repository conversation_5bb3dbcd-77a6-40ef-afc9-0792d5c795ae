<apex:page id="changePassword" showHeader="false" controller="ChangePasswordController" title="{!$Label.site.change_password}">
  <apex:composition template="{!$Site.Template}">
    <apex:define name="body">  
      <center>
        <apex:panelGrid bgcolor="white" columns="1"> 
          <br/>
          <br/>
          <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="1" styleClass="topPanelContainer"> 
            <br/>
            <apex:outputPanel layout="block" styleClass="topPanel">
              <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="2"> 
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/clock.png')}"/>
                <apex:panelGroup >
                  <br/>
                  <apex:outputText styleClass="title" value="{!$Label.site.change_your_password}"/>
                  <br/>
                  <apex:form id="theForm">
                    <apex:pageMessages id="error"/>
                    <apex:panelGrid columns="2" style="margin-top:1em;">
                      <apex:outputLabel rendered="{! !$Site.IsPasswordExpired}" value="{!$Label.site.old_password}" for="oldpsw"/>
                      <apex:inputSecret required="true" id="oldpsw" rendered="{! !$Site.IsPasswordExpired}" value="{!oldPassword}"/>
                      <apex:outputLabel value="{!$Label.site.new_password}" for="psw"/>
                      <apex:inputSecret required="true" id="psw" value="{!newPassword}"/>
                      <apex:outputLabel value="{!$Label.site.verify_new_password}" for="vpsw"/>
                      <apex:inputSecret required="true" id="vpsw" value="{!verifyNewPassword}"/>
                      <apex:outputText value=""/>
                      <apex:commandButton id="cpwbtn" action="{!changePassword}" value="{!$Label.site.change_password}"/>
                    </apex:panelGrid> 
                  </apex:form>                  
                  <br/>
                </apex:panelGroup>
              </apex:panelGrid> 
             </apex:outputPanel>
            <c:SiteFooter />
          </apex:panelGrid> 
       </apex:panelGrid>
      </center>
      <br/>
    </apex:define>
  </apex:composition>
</apex:page>
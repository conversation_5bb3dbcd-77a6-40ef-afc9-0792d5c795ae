<apex:page id="portalUserProfilePage" controller="MyProfilePageController" showHeader="false" title="{!$Label.site.my_profile_page}">
  <apex:composition template="{!$Site.Template}">
    <apex:define name="body">
        <apex:form id="theForm">
          <apex:outputPanel style="padding:50px 50px 50px 50px" id="userDetail">
           <apex:pageBlock title="{!$Label.site.my_profile}" id="editUserDetail" rendered="{!isEdit}">
             <apex:pageMessages />
             <apex:pageBlockButtons id="editButtons" location="top">
               <apex:commandButton id="save" value="{!$Label.site.save}" action="{!save}" rerender="userDetail"/>
               <apex:commandButton id="cancel" value="{!$Label.site.cancel}" action="{!cancel}" rerender="userDetail" immediate="true"/>
             </apex:pageBlockButtons>
             <apex:pageBlockSection id="editUserSection" columns="1" title="{!$Label.site.user_information}">
                 <apex:inputField id="editusername" value="{!user.username}"/>
                 <apex:inputField id="edittimezone" value="{!user.timezonesidkey}"/> 
                 <apex:inputField id="editlocale" value="{!user.localesidkey}"/> 
                 <apex:inputField id="editlanguage" value="{!user.languagelocalekey}"/> 
                 <apex:inputField id="editcommunityNickname" value="{!user.communityNickname}"/> 
             </apex:pageBlockSection>
             <apex:pageBlockSection id="editContactSection" columns="2" title="{!$Label.site.contact_information}">
                 <apex:inputField id="editfirstName" value="{!user.firstName}"/> 
                 <apex:inputField id="editemail" value="{!user.email}"/> 
                 <apex:inputField id="editlastName" value="{!user.lastName}"/> 
                 <apex:inputField id="editphone" value="{!user.phone}"/> 
                 <apex:inputField id="edittitle" value="{!user.title}"/> 
                 <apex:inputField id="editextension" value="{!user.extension}"/> 
                 <apex:pageBlockSectionItem />
                 <apex:inputField id="editfax" value="{!user.fax}"/>
                 <apex:pageBlockSectionItem />
                 <apex:inputField id="editmobile" value="{!user.mobilephone}"/> 
               </apex:pageBlockSection>
               <apex:pageBlockSection id="editAddressInformation" columns="1" title="{!$Label.site.address_information}">
                 <apex:inputField id="editstreet" value="{!user.street}"/> 
                 <apex:inputField id="editcity" value="{!user.city}"/> 
                 <apex:inputField id="editstate" value="{!user.state}"/> 
                 <apex:inputField id="editpostalcode" value="{!user.postalcode}"/> 
                 <apex:inputField id="editcountry" value="{!user.country}"/> 
               </apex:pageBlockSection>
           </apex:pageBlock>         
        
            <apex:pageBlock title="{!$Label.site.my_profile}" id="viewUserDetail" rendered="{!!isEdit}">
                 <apex:pageBlockButtons id="detailButtons" location="top">
                   <apex:commandButton id="edit" value="{!$Label.site.edit}" action="{!edit}" rerender="userDetail"/>
                   <apex:commandButton id="changePassword" value="{!$Label.site.change_password}" action="{!changePassword}"/>
                 </apex:pageBlockButtons>
                 <apex:pageBlockSection id="viewUserSection" columns="2" title="{!$Label.site.user_information}">
                     <apex:outputField id="detailusername" value="{!user.username}"/>
                     <apex:outputField id="detailtimezone" value="{!user.timezonesidkey}"/> 
                     <apex:outputField id="detaillocale" value="{!user.localesidkey}"/> 
                     <apex:outputField id="detaillanguage" value="{!user.languagelocalekey}"/> 
                     <apex:outputField id="detailcommunityNickname" value="{!user.communityNickname}"/> 
                 </apex:pageBlockSection>
                 <apex:pageBlockSection id="viewContactSection" columns="2" title="{!$Label.site.contact_information}">
                     <apex:outputField id="detailemail" value="{!user.email}"/> 
                     <apex:outputField id="detailfirstName" value="{!user.firstName}"/> 
                     <apex:outputField id="detaillastName" value="{!user.lastName}"/> 
                     <apex:outputField id="detailtitle" value="{!user.title}"/> 
                     <apex:outputField id="detailphone" value="{!user.phone}"/> 
                     <apex:outputField id="detailstreet" value="{!user.street}"/> 
                     <apex:outputField id="detailcity" value="{!user.city}"/> 
                     <apex:outputField id="detailstate" value="{!user.state}"/> 
                     <apex:outputField id="detailpostalcode" value="{!user.postalcode}"/> 
                     <apex:outputField id="detailcountry" value="{!user.country}"/> 
                     <apex:outputField id="detailextension" value="{!user.extension}"/> 
                     <apex:outputField id="detailfax" value="{!user.fax}"/> 
                     <apex:outputField id="detailmobile" value="{!user.mobilephone}"/> 
                  </apex:pageBlockSection>
               </apex:pageBlock>
            </apex:outputPanel>
         </apex:form>
    </apex:define>
  </apex:composition>
</apex:page>
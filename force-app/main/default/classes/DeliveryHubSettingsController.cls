/**
 * @description Controller class for managing Delivery Hub application settings
 * <AUTHOR> Hub Team
 * @date 2025
 */
public with sharing class DeliveryHubSettingsController {

    // Legacy wrapper class - commented out for future reference
    // public class SettingsWrapper { ... }

    /**
     * @description Fetches the application settings for the current context.
     * @return SettingsWrapper The current settings.
     */
   /* @AuraEnabled(cacheable=true)
    public static SettingsWrapper getSettings() {
        // getInstance() automatically gets the settings for the current user/profile/org
        Delivery_Hub_Settings__c settings = Delivery_Hub_Settings__c.getInstance();
        
        if (settings == null) { 
            // Return defaults if no settings record exists yet
            settings = Delivery_Hub_Settings__c.getOrgDefaults();
        }

        SettingsWrapper wrapper = new SettingsWrapper();
        wrapper.aiSuggestionsEnabled = settings.AI_Suggestions_Enabled__c;
        wrapper.autoGenerateDescriptions = settings.Auto_Generate_Descriptions__c;
        wrapper.aiEstimationEnabled = settings.AI_Estimation_Enabled__c;
        wrapper.openaiApiKey = settings.OpenAI_API_Key__c;
        wrapper.openaiModel = settings.OpenAI_Model__c;
        wrapper.jiraEnabled = settings.JIRA_Enabled__c;
        wrapper.jiraUrl = settings.JIRA_Instance_URL__c;
        wrapper.jiraUsername = settings.JIRA_Username__c;
        wrapper.jiraApiToken = settings.JIRA_API_Token__c;
        wrapper.jiraProjectKey = settings.JIRA_Project_Key__c;

        return wrapper;
    }*/

    /**
     * @description Saves the application settings.
     * @param settingsJson A JSON string of the SettingsWrapper.
     */
   /* @AuraEnabled
    public static void saveSettings(String settingsJson) {
        SettingsWrapper wrapper = (SettingsWrapper) JSON.deserialize(settingsJson, SettingsWrapper.class);

        // Upsert the org-wide settings record
        Delivery_Hub_Settings__c settings = Delivery_Hub_Settings__c.getOrgDefaults();
        if (settings == null) {
            settings = new Delivery_Hub_Settings__c(SetupOwnerId = UserInfo.getOrganizationId());
        }
        
        settings.AI_Suggestions_Enabled__c = wrapper.aiSuggestionsEnabled;
        settings.Auto_Generate_Descriptions__c = wrapper.autoGenerateDescriptions;
        settings.AI_Estimation_Enabled__c = wrapper.aiEstimationEnabled;
        settings.OpenAI_API_Key__c = wrapper.openaiApiKey;
        settings.OpenAI_Model__c = wrapper.openaiModel;
        settings.JIRA_Enabled__c = wrapper.jiraEnabled;
        settings.JIRA_Instance_URL__c = wrapper.jiraUrl;
        settings.JIRA_Username__c = wrapper.jiraUsername;
        settings.JIRA_API_Token__c = wrapper.jiraApiToken;
        settings.JIRA_Project_Key__c = wrapper.jiraProjectKey;

        // Check FLS permissions before upsert
        SObjectAccessDecision decision = Security.stripInaccessible(
            AccessType.UPSERTABLE, new List<Delivery_Hub_Settings__c>{settings});
        upsert decision.getRecords();
    }*/

    /**
     * @description Saves only OpenAI settings, preserving other settings.
     * @param apiKey The OpenAI API key
     * @param model The OpenAI model to use
     */
   /* @AuraEnabled
    public static void saveOpenAISettings(String apiKey, String model) {
        Delivery_Hub_Settings__c settings = Delivery_Hub_Settings__c.getOrgDefaults();
        if (settings == null) {
            settings = new Delivery_Hub_Settings__c(SetupOwnerId = UserInfo.getOrganizationId());
        }
        
        settings.OpenAI_API_Key__c = apiKey;
        settings.OpenAI_Model__c = model;

        upsert settings;
    }*/

    /**
     * @description Saves only JIRA settings, preserving other settings.
     * @param enabled Whether JIRA integration is enabled
     * @param jiraUrl The JIRA instance URL
     * @param username The JIRA username
     * @param apiToken The JIRA API token
     * @param projectKey The JIRA project key
     */
  /*  @AuraEnabled
    public static void saveJiraSettings(Boolean enabled, String jiraUrl, String username, String apiToken, String projectKey) {
        Delivery_Hub_Settings__c settings = Delivery_Hub_Settings__c.getOrgDefaults();
        if (settings == null) {
            settings = new Delivery_Hub_Settings__c(SetupOwnerId = UserInfo.getOrganizationId());
        }
        
        settings.JIRA_Enabled__c = enabled;
        settings.JIRA_Instance_URL__c = jiraUrl;
        settings.JIRA_Username__c = username;
        settings.JIRA_API_Token__c = apiToken;
        settings.JIRA_Project_Key__c = projectKey;

        upsert settings;
    }*/

    /**
     * @description Tests the provided OpenAI API key.
     * @param apiKey The API key to test.
     * @return String 'Success' or an error message.
     */
    @AuraEnabled
    public static String testOpenAIConnection(String apiKey) {
        // This is a simplified test. It uses the same logic as the real callout
        // but with a very simple prompt to minimize token usage.
        HttpRequest req = new HttpRequest();
        req.setEndpoint('https://api.openai.com/v1/chat/completions');
        req.setMethod('POST');
        // TODO: Replace with Named Credential for better security
        req.setHeader('Authorization', 'Bearer ' + apiKey);
        req.setHeader('Content-Type', 'application/json');
        req.setBody('{"model": "gpt-4o-mini", "messages": [{"role": "user", "content": "Test"}], "max_tokens": 5}');

        try {
            HttpResponse res = new Http().send(req);
            if (res.getStatusCode() >= 200 && res.getStatusCode() < 300) {
                return 'Success';
            } else {
                return 'Failed: ' + res.getStatusCode() + ' - ' + res.getBody();
            }
        } catch (Exception e) {
            return 'Error: ' + e.getMessage();
        }
    }

    /**
     * @description Tests JIRA connection with provided credentials.
     * @param jiraUrl The JIRA instance URL
     * @param username The JIRA username/email
     * @param apiToken The JIRA API token
     * @param projectKey The JIRA project key to test
     * @return String 'Success' or an error message.
     */
    @AuraEnabled
    public static String testJiraConnection(String jiraUrl, String username, String apiToken, String projectKey) {
        try {
            // Test by trying to get project information
            HttpRequest req = new HttpRequest();
            req.setEndpoint(jiraUrl + '/rest/api/2/project/' + projectKey);
            req.setMethod('GET');
            System.debug(LoggingLevel.DEBUG, 'Testing JIRA connection to: ' + jiraUrl + '/rest/api/2/project/' + projectKey);
            // Create basic auth header
            // TODO: Replace with Named Credential for better security
            String credentials = username + ':' + apiToken;
            String encodedCredentials = EncodingUtil.base64Encode(Blob.valueOf(credentials));
            req.setHeader('Authorization', 'Basic ' + encodedCredentials);
            req.setHeader('Content-Type', 'application/json');
            req.setTimeout(30000); // 30 second timeout

            HttpResponse res = new Http().send(req);
            
            if (res.getStatusCode() == 200) {
                // Parse response to verify project exists
                Map<String, Object> projectData = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String returnedKey = (String) projectData.get('key');
                
                if (returnedKey != null && returnedKey.equalsIgnoreCase(projectKey)) {
                    return 'Success';
                } else {
                    return 'Project key mismatch. Expected: ' + projectKey + ', Found: ' + returnedKey;
                }
            } else if (res.getStatusCode() == 401) {
                return 'Authentication failed. Please check your username and API token.';
            } else if (res.getStatusCode() == 404) {
                return 'Project not found. Please check the project key: ' + projectKey;
            } else {
                return 'Failed: ' + res.getStatusCode() + ' - ' + res.getStatus();
            }
        } catch (Exception e) {
            return 'Connection error: ' + e.getMessage();
        }
    }
    
    /**
     * @description Data Transfer Object for settings data
     */
      public class SettingsDTO {
        /** @description Enable notifications setting */
        @AuraEnabled public Boolean enableNotifications { get; set; }
        /** @description AI suggestions enabled setting */
        @AuraEnabled public Boolean aiSuggestionsEnabled { get; set; }
        /** @description Auto generate descriptions setting */
        @AuraEnabled public Boolean autoGenerateDescriptions { get; set; }
        /** @description AI estimation enabled setting */
        @AuraEnabled public Boolean aiEstimationEnabled { get; set; }
        /** @description OpenAI API key */
        @AuraEnabled public String openaiApiKey { get; set; }
        /** @description OpenAI model name */
        @AuraEnabled public String openaiModel { get; set; }
        /** @description JIRA integration enabled setting */
        @AuraEnabled public Boolean jiraEnabled { get; set; }
        /** @description JIRA instance URL */
        @AuraEnabled public String jiraInstanceUrl { get; set; }
        /** @description JIRA username */
        @AuraEnabled public String jiraUsername { get; set; }
        /** @description JIRA API token */
        @AuraEnabled public String jiraApiToken { get; set; }
        /** @description JIRA project key */
        @AuraEnabled public String jiraProjectKey { get; set; }
        /** @description JIRA API tested status */
        @AuraEnabled public Boolean jiraApiTested { get; set; }
        /** @description OpenAI API tested status */
        @AuraEnabled public Boolean openAiApiTested { get; set; }

    }

    /**
     * @description Fetches all settings for all tabs.
     */
    @AuraEnabled(cacheable=true)
    public static SettingsDTO getSettings() {
        Delivery_Hub_Settings__c settings = Delivery_Hub_Settings__c.getOrgDefaults();
        SettingsDTO dto = new SettingsDTO();

        if (settings != null) {
            dto.enableNotifications = settings.Enable_Notifications__c;
            dto.aiSuggestionsEnabled = settings.AI_Suggestions_Enabled__c;
            dto.autoGenerateDescriptions = settings.Auto_Generate_Descriptions__c;
            dto.aiEstimationEnabled = settings.AI_Estimation_Enabled__c;
            dto.openaiApiKey = settings.OpenAI_API_Key__c;
            dto.openaiModel = settings.OpenAI_Model__c;
            dto.jiraEnabled = settings.JIRA_Enabled__c;
            dto.jiraInstanceUrl = settings.JIRA_Instance_URL__c;
            dto.jiraUsername = settings.JIRA_Username__c;
            dto.jiraApiToken = settings.JIRA_API_Token__c;
            dto.jiraProjectKey = settings.JIRA_Project_Key__c;
            dto.jiraApiTested = settings.JIra_Api_tested__c;
            dto.openAiApiTested = settings.Open_Ai_Api_tested__c;
        }
        return dto;
    }

    /**
     * @description Gets existing settings or creates new default settings
     * @return Delivery_Hub_Settings__c settings record
     */
    private static Delivery_Hub_Settings__c getOrCreateSettings() {
        Delivery_Hub_Settings__c settings = Delivery_Hub_Settings__c.getOrgDefaults();
        if (settings == null) {
            settings = new Delivery_Hub_Settings__c(SetupOwnerId = UserInfo.getOrganizationId());
        }
        return settings;
    }

    /**
     * @description Saves general application settings
     * @param enableNotifications Whether to enable notifications
     */
    @AuraEnabled
    public static void saveGeneralSettings(Boolean enableNotifications) {
        Delivery_Hub_Settings__c settings = getOrCreateSettings();
        settings.Enable_Notifications__c = enableNotifications;
        // Check FLS permissions before upsert
        SObjectAccessDecision decision = Security.stripInaccessible(
            AccessType.UPSERTABLE, new List<Delivery_Hub_Settings__c>{settings});
        upsert decision.getRecords();
    }

    /**
     * @description Saves AI-related settings
     * @param suggestions Enable AI suggestions
     * @param descriptions Enable auto-generate descriptions
     * @param estimation Enable AI estimation
     */
    @AuraEnabled
    public static void saveAiSettings(Boolean suggestions, Boolean descriptions, Boolean estimation) {
        Delivery_Hub_Settings__c settings = getOrCreateSettings();
        settings.AI_Suggestions_Enabled__c = suggestions;
        settings.Auto_Generate_Descriptions__c = descriptions;
        settings.AI_Estimation_Enabled__c = estimation;
        System.debug(LoggingLevel.DEBUG, 'settings.AI_Suggestions_Enabled__c '+settings.AI_Suggestions_Enabled__c);
        System.debug(LoggingLevel.DEBUG, 'settings.Auto_Generate_Descriptions__c '+settings.Auto_Generate_Descriptions__c);
        System.debug(LoggingLevel.DEBUG, 'settings.AI_Estimation_Enabled__c '+settings.AI_Estimation_Enabled__c);
        // Check FLS permissions before upsert
        SObjectAccessDecision decision = Security.stripInaccessible(
            AccessType.UPSERTABLE, new List<Delivery_Hub_Settings__c>{settings});
        upsert decision.getRecords();
    }
    
    /**
     * @description Saves OpenAI API settings
     * @param apiKey OpenAI API key
     * @param model OpenAI model name
     * @param tested Whether the API has been tested
     */
    @AuraEnabled
    public static void saveOpenAISettings(String apiKey, String model, boolean tested) {
        Delivery_Hub_Settings__c settings = getOrCreateSettings();
        settings.OpenAI_API_Key__c = apiKey;
        settings.OpenAI_Model__c = model;
        settings.Open_Ai_Api_tested__c = tested;
        // Check FLS permissions before upsert
        SObjectAccessDecision decision = Security.stripInaccessible(
            AccessType.UPSERTABLE, new List<Delivery_Hub_Settings__c>{settings});
        upsert decision.getRecords();
    }

    @AuraEnabled
    public static void saveJiraSettings( String url, String username, String token, String projectKey, Boolean isVerified) {
        Delivery_Hub_Settings__c settings = getOrCreateSettings();
        // settings.JIRA_Enabled__c = enabled;
        settings.JIRA_Instance_URL__c = url;
        settings.JIRA_Username__c = username;
        settings.JIRA_API_Token__c = token;
        settings.JIRA_Project_Key__c = projectKey;
    settings.Jira_Api_tested__c = isVerified;
        upsert settings;
    }

    /**
     * @description Saves JIRA enabled state
     * @param enabled Whether JIRA integration is enabled
     */
    @AuraEnabled
    public static void saveJiraEnabledState(Boolean enabled) {
        Delivery_Hub_Settings__c settings = getOrCreateSettings();
        settings.JIRA_Enabled__c = enabled;
        // Can't Check FLS permissions before upsert for Custom setting
        upsert settings;
    }

   
    

}
public with sharing class KanbanSettingsController {

    // Wrapper class to pass settings data to and from the LWC
    public class SettingsWrapper {
        @AuraEnabled public Boolean aiSuggestionsEnabled { get; set; }
        @AuraEnabled public Boolean autoGenerateDescriptions { get; set; }
        @AuraEnabled public Boolean aiEstimationEnabled { get; set; }
        @AuraEnabled public String openaiApiKey { get; set; }
        @AuraEnabled public String openaiModel { get; set; }
        @AuraEnabled public Boolean jiraEnabled { get; set; }
        @AuraEnabled public String jiraUrl { get; set; }
        @AuraEnabled public String jiraUsername { get; set; }
        @AuraEnabled public String jiraApiToken { get; set; }
        @AuraEnabled public String jiraProjectKey { get; set; }
    }

    /**
     * @description Fetches the application settings for the current context.
     * @return SettingsWrapper The current settings.
     */
    @AuraEnabled(cacheable=true)
    public static SettingsWrapper getSettings() {
        // getInstance() automatically gets the settings for the current user/profile/org
        Kanban_App_Settings__c settings = Kanban_App_Settings__c.getInstance();
        
        if (settings == null) { 
            // Return defaults if no settings record exists yet
            settings = Kanban_App_Settings__c.getOrgDefaults();
        }

        SettingsWrapper wrapper = new SettingsWrapper();
        wrapper.aiSuggestionsEnabled = settings.AI_Suggestions_Enabled__c;
        wrapper.autoGenerateDescriptions = settings.Auto_Generate_Descriptions__c;
        wrapper.aiEstimationEnabled = settings.AI_Estimation_Enabled__c;
        wrapper.openaiApiKey = settings.OpenAI_API_Key__c;
        wrapper.openaiModel = settings.OpenAI_Model__c;
        wrapper.jiraEnabled = settings.JIRA_Enabled__c;
        wrapper.jiraUrl = settings.JIRA_Instance_URL__c;
        wrapper.jiraUsername = settings.JIRA_Username__c;
        wrapper.jiraApiToken = settings.JIRA_API_Token__c;
        wrapper.jiraProjectKey = settings.JIRA_Project_Key__c;

        return wrapper;
    }

    /**
     * @description Saves the application settings.
     * @param settingsJson A JSON string of the SettingsWrapper.
     */
    @AuraEnabled
    public static void saveSettings(String settingsJson) {
        SettingsWrapper wrapper = (SettingsWrapper) JSON.deserialize(settingsJson, SettingsWrapper.class);

        // Upsert the org-wide settings record
        Kanban_App_Settings__c settings = Kanban_App_Settings__c.getOrgDefaults();
        if (settings == null) {
            settings = new Kanban_App_Settings__c(SetupOwnerId = UserInfo.getOrganizationId());
        }
        
        settings.AI_Suggestions_Enabled__c = wrapper.aiSuggestionsEnabled;
        settings.Auto_Generate_Descriptions__c = wrapper.autoGenerateDescriptions;
        settings.AI_Estimation_Enabled__c = wrapper.aiEstimationEnabled;
        settings.OpenAI_API_Key__c = wrapper.openaiApiKey;
        settings.OpenAI_Model__c = wrapper.openaiModel;
        settings.JIRA_Enabled__c = wrapper.jiraEnabled;
        settings.JIRA_Instance_URL__c = wrapper.jiraUrl;
        settings.JIRA_Username__c = wrapper.jiraUsername;
        settings.JIRA_API_Token__c = wrapper.jiraApiToken;
        settings.JIRA_Project_Key__c = wrapper.jiraProjectKey;

        upsert settings;
    }

    /**
     * @description Tests the provided OpenAI API key.
     * @param apiKey The API key to test.
     * @return String 'Success' or an error message.
     */
    @AuraEnabled
    public static String testOpenAIConnection(String apiKey) {
        // This is a simplified test. It uses the same logic as the real callout
        // but with a very simple prompt to minimize token usage.
        HttpRequest req = new HttpRequest();
        req.setEndpoint('https://api.openai.com/v1/chat/completions');
        req.setMethod('POST');
        req.setHeader('Authorization', 'Bearer ' + apiKey);
        req.setHeader('Content-Type', 'application/json');
        req.setBody('{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Test"}], "max_tokens": 5}');

        try {
            HttpResponse res = new Http().send(req);
            if (res.getStatusCode() >= 200 && res.getStatusCode() < 300) {
                return 'Success';
            } else {
                return 'Failed: ' + res.getStatusCode() + ' - ' + res.getBody();
            }
        } catch (Exception e) {
            return 'Error: ' + e.getMessage();
        }
    }
}
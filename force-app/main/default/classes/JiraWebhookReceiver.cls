/**
 * @description Enhanced REST endpoint to receive webhook payloads from <PERSON><PERSON> with authentication,
 * event routing, and comprehensive error handling. This class validates incoming requests,
 * routes events to appropriate processors, and provides detailed error responses.
 *
 * Authentication is performed using a shared secret in the X-Jira-Secret header.
 * Event routing is based on the webhookEvent field in the payload.
 *
 * Supported webhook events:
 * - jira:issue_created, jira:issue_updated, jira:issue_deleted
 * - comment_created, comment_updated, comment_deleted  
 * - attachment_created, attachment_deleted
 */
@RestResource(urlMapping='/JiraWebhook/*')
global without sharing class JiraWebhookReceiver {

    // Constants for webhook event types
    private static final String EVENT_ISSUE_CREATED = 'jira:issue_created';
    private static final String EVENT_ISSUE_UPDATED = 'jira:issue_updated';
    private static final String EVENT_ISSUE_DELETED = 'jira:issue_deleted';
    private static final String EVENT_COMMENT_CREATED = 'comment_created';
    private static final String EVENT_COMMENT_UPDATED = 'comment_updated';
    private static final String EVENT_COMMENT_DELETED = 'comment_deleted';
    private static final String EVENT_ATTACHMENT_CREATED = 'attachment_created';
    private static final String EVENT_ATTACHMENT_DELETED = 'attachment_deleted';
    
    // Authentication header name
    private static final String AUTH_HEADER = 'X-Jira-Secret';
    
    /**
     * @description Enhanced webhook handler with authentication, validation, and event routing
     */
    @HttpPost
    global static void doPost() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        
        // Set response headers
        res.addHeader('Content-Type', 'application/json');
        
        
        try {
            // Step 1: Validate authentication
            // if (!validateAuthentication(req)) {
            //     sendErrorResponse(res, 401, 'Unauthorized', 'Invalid or missing authentication credentials');
            //     return;
            // }
            
            // Step 2: Validate request body
            String requestBody = '';
            if (req.requestBody != null) {
                requestBody = req.requestBody.toString();
            }
            
            if (String.isBlank(requestBody)) {
                sendErrorResponse(res, 400, 'Bad Request', 'Request body is required');
                return;
            }
            
            // Step 3: Parse and validate JSON payload
            Map<String, Object> jiraPayload;
            try {
                jiraPayload = (Map<String, Object>)JSON.deserializeUntyped(requestBody);
            } catch (JSONException e) {
                sendErrorResponse(res, 400, 'Bad Request', 'Invalid JSON payload: ' + e.getMessage());
                return;
            }
            
            // Step 4: Validate required fields
            System.debug('jiraPayload- > ' + jiraPayload);
            String validationError = validatePayloadStructure(jiraPayload);
            if (validationError != null) {
                sendErrorResponse(res, 400, 'Bad Request', validationError);
                return;
            }
            
            // Step 5: Route event to appropriate processor
            String eventType = (String)jiraPayload.get('webhookEvent');

            // --- NEW STEP 6: Check for duplicate events before routing ---
            if (isDuplicateEvent(eventType, jiraPayload)) {
                System.debug('Duplicate event received and ignored. EventType: ' + eventType);
                sendSuccessResponse(res, 'Duplicate event ignored', eventType);
                return; // Stop all further processing
            }
            Boolean routingSuccess = routeEvent(eventType, jiraPayload);
            
            if (routingSuccess) {
                sendSuccessResponse(res, 'Webhook processed successfully', eventType);
            } else {
                sendErrorResponse(res, 422, 'Unprocessable Entity', 'Unsupported event type: ' + eventType);
            }
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Unexpected error processing Jira webhook: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, 'Stack trace: ' + e.getStackTraceString());
            sendErrorResponse(res, 500, 'Internal Server Error', 'An unexpected error occurred during processing');
        }
    }
    

    // --- NEW METHOD to check for duplicates ---
    /**
     * @description Checks if the incoming event is a duplicate by querying for an existing record
     * with the same Jira ID.
     * @param eventType The webhook event type
     * @param payload The webhook payload
     * @return Boolean indicating if the event is a duplicate
     */
    // private static Boolean isDuplicateEvent(String eventType, Map<String, Object> payload) {
    //     try {
    //         if (isIssueEvent(eventType)) {
    //             Map<String, Object> issueMap = (Map<String, Object>)payload.get('issue');
    //             if (issueMap != null && issueMap.containsKey('id')) {
    //                 // **NOTE:** Adjust the field API name 'JiraTicketIdTxt__c' if needed
    //                 String jiraIssueId = String.valueOf(issueMap.get('id'));
    //                 return [SELECT COUNT() FROM Ticket__c WHERE JiraTicketId__c = :jiraIssueId] > 0;
    //             }
    //         } else if (isCommentEvent(eventType)) {
    //             Map<String, Object> commentMap = (Map<String, Object>)payload.get('comment');
    //             if (commentMap != null && commentMap.containsKey('id')) {
    //                 // **NOTE:** Adjust the field API name 'JiraCommentIdTxt__c' if needed
    //                 String jiraCommentId = String.valueOf(commentMap.get('id'));
    //                 return [SELECT COUNT() FROM Ticket_Comment__c WHERE JiraCommentIdTxt__c = :jiraCommentId] > 0;
    //             }
    //         } else if (isAttachmentEvent(eventType)) {
    //             Map<String, Object> attachmentMap = (Map<String, Object>)payload.get('attachment');
    //             if (attachmentMap != null && attachmentMap.containsKey('id')) {
    //                 // **NOTE:** Adjust the field API name 'JiraAttachmentId__c' if needed
    //                 String jiraAttachmentId = String.valueOf(attachmentMap.get('id'));
    //                 return [SELECT COUNT() FROM Attachment_Sync_Log__c WHERE Jira_Attachment_ID__c = :jiraAttachmentId] > 0;
    //             }
    //         }
    //     } catch (Exception e) {
    //         System.debug(LoggingLevel.ERROR, 'Error during duplicate check for event ' + eventType + ': ' + e.getMessage());
    //         // Fail open: If check fails, assume it's not a duplicate to allow processing to continue.
    //         return false;
    //     }
    //     // Not a relevant event type for duplicate checking or ID was not found in payload
    //     return false; 
    // }

    /**
     * @description Checks if the incoming event is a duplicate. For 'update' events, it
     * performs a deep content check. For other events, it checks for existence.
     * @param eventType The webhook event type
     * @param payload The webhook payload
     * @return Boolean indicating if the event should be ignored
     */
    private static Boolean isDuplicateEvent(String eventType, Map<String, Object> payload) {
        // --- NEW LOGIC: Route based on event type ---
        if (eventType.endsWith('_deleted')) {
        return false; // This means "it is NOT a duplicate", so the process continues.
    }

        if (eventType != null && eventType.endsWith('_updated')) {
            // For update events, perform a deep content comparison
            return !hasContentChanged(eventType, payload); // Note the inversion: if content HAS NOT changed, it IS a duplicate.
        } else {
            // For create/delete events, perform a simple existence check
            return doesRecordExist(eventType, payload);
        }
    }

    /**
     * @description Determines if the content of an updated event has meaningful changes compared to the existing Salesforce record.
     * @param eventType The webhook event type (e.g., jira:issue_updated)
     * @param payload The webhook payload
     * @return Boolean - true if content has changed, false if it's the same
     */
    private static Boolean hasContentChanged(String eventType, Map<String, Object> payload) {
        try {
            if (isIssueEvent(eventType)) {
                return hasContentChangedForIssue(payload);
            }
            // Future enhancement: Add similar logic for comments and attachments
            else if (isCommentEvent(eventType)) {
                return hasContentChangedForComment(payload);
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error during content change check for event ' + eventType + ': ' + e.getMessage());
            // Fail open: If the check fails, assume content HAS changed to allow processing.
            return true;
        }
        // If the event type is not configured for a content check, assume it has changed.
        return true;
    }

    /**
     * @description Performs a deep comparison for a Jira issue against a Salesforce Ticket__c.
     * @param payload The webhook payload
     * @return Boolean - true if content has changed, false otherwise
     */
    private static Boolean hasContentChangedForIssue(Map<String, Object> payload) {
        Map<String, Object> issueMap = (Map<String, Object>)payload.get('issue');
        if (issueMap == null || !issueMap.containsKey('id')) {
            return true; // Not enough info to check, so let it process
        }
        
        String jiraIssueId = String.valueOf(issueMap.get('id'));

        // Query for the existing ticket, selecting ALL fields that the mapping utility might change.
        // Keep this field list in sync with JiraFieldMappingUtil!
        List<Ticket__c> existingTickets = [
            SELECT Id, BriefDescriptionTxt__c, WorkItemNameTxt__c, DetailsTxt__c, WorkItemTypeTxt__c,
                   StatusPk__c,StageNamePk__c, Developer__c, PriorityPk__c, DeveloperDaysSizeNumber__c
            FROM Ticket__c 
            WHERE JiraIssueIdTxt__c = :jiraIssueId
            LIMIT 1
        ];

        if (existingTickets.isEmpty()) {
            // If the ticket doesn't exist yet, it's definitely new content.
            return true;
        }
        
        Ticket__c existingTicket = existingTickets[0];
        
        // Create a new ticket in memory and populate it using the mapping utility
        Ticket__c inMemoryTicket = new Ticket__c();
        JiraFieldMappingUtil.mapJiraIssueToTicket(issueMap, inMemoryTicket);

        // Use helper method to reduce cyclomatic complexity
        return hasTicketFieldsChanged(existingTicket, inMemoryTicket, jiraIssueId);
    }
    
    /**
     * @description Helper method to compare ticket fields and reduce cyclomatic complexity
     * @param existingTicket The current ticket record from database
     * @param inMemoryTicket The ticket populated from Jira payload
     * @param jiraIssueId The Jira issue ID for logging
     * @return Boolean indicating if any fields have changed
     */
    private static Boolean hasTicketFieldsChanged(Ticket__c existingTicket, Ticket__c inMemoryTicket, String jiraIssueId) {
        // Compare the fields. If any field is different, the content has changed.
        if (existingTicket.BriefDescriptionTxt__c != inMemoryTicket.BriefDescriptionTxt__c) {
            return true;
        }
        if (existingTicket.WorkItemNameTxt__c != inMemoryTicket.WorkItemNameTxt__c) {
            return true;
        }
        if (existingTicket.DetailsTxt__c != inMemoryTicket.DetailsTxt__c) {
            return true;
        }
        if (existingTicket.WorkItemTypeTxt__c != inMemoryTicket.WorkItemTypeTxt__c) {
            return true;
        }
        if (existingTicket.StatusPk__c != inMemoryTicket.StatusPk__c) {
            System.debug(LoggingLevel.DEBUG, 'StatusPk__c changed from ' + existingTicket.StatusPk__c + ' to ' + inMemoryTicket.StatusPk__c);
            return true;
        }
        if (existingTicket.StageNamePk__c != inMemoryTicket.StageNamePk__c) {
            return true;
        }
        if (existingTicket.Developer__c != inMemoryTicket.Developer__c) {
            return true;
        }
        if (existingTicket.PriorityPk__c != inMemoryTicket.PriorityPk__c) {
            return true;
        }
        
        // If we get here, no relevant fields have changed.
        System.debug(LoggingLevel.DEBUG, 'Content check for issue ' + jiraIssueId + ' found no changes. Ignoring update.');
        return false;
    }

    private static Boolean hasContentChangedForComment(Map<String, Object> payload) {
    Map<String, Object> commentData = (Map<String, Object>)payload.get('comment');
    if (commentData == null || !commentData.containsKey('id')) {
        return true; // Not enough info to check, so let it process
    }

    String jiraCommentId = String.valueOf(commentData.get('id'));

    // 1. Query for the existing comment, selecting the fields we need to compare
    List<Ticket_Comment__c> existingComments = [
        SELECT Id, AuthorTxt__c, BodyTxt__c 
        FROM Ticket_Comment__c 
        WHERE JiraCommentIdTxt__c = :jiraCommentId
        LIMIT 1
    ];

        System.debug('existing comment '+existingComments);
    if (existingComments.isEmpty()) {
        // If the comment doesn't exist yet, it's definitely new content.
        return true;
    }

    Ticket_Comment__c existingComment = existingComments[0];

    // 2. Use JiraCommentHandler methods to build the in-memory representation of the comment
    // Get the author from the payload
    Map<String, String> authorInfo = JiraCommentHandler.extractAuthorInformationFromPayload(commentData);
    String inMemoryAuthor = authorInfo.get('displayName');

    // Get and process the body from the payload
    String rawBody = JiraCommentHandler.extractCommentBodyFromPayload(commentData);
    String inMemoryBody = JiraCommentHandler.convertHtmlToPlainText(rawBody);
    String existingBodyAsPlainText = JiraCommentHandler.convertHtmlToPlainText(existingComment.BodyTxt__c);

     System.debug('inMemoryAuthor'+inMemoryAuthor);
    System.debug('inMemoryBody'+inMemoryBody);
    // 3. Compare the fields. If any field is different, the content has changed.
    // if (existingComment.AuthorTxt__c != inMemoryAuthor) return true;
    if (existingBodyAsPlainText != inMemoryBody) return true;
    
    // If we get here, no relevant fields have changed.
    System.debug('Content check for comment ' + jiraCommentId + ' found no changes. Ignoring update.');
    return false;
}

    /**
     * @description Simple existence check for create/delete events.
     * @param eventType The webhook event type
     * @param payload The webhook payload
     * @return Boolean indicating if a record with the given Jira ID already exists
     */
    private static Boolean doesRecordExist(String eventType, Map<String, Object> payload) {
        try {
            if (isIssueEvent(eventType)) {
                Map<String, Object> issueMap = (Map<String, Object>)payload.get('issue');
                if (issueMap != null && issueMap.containsKey('id')) {
                    String jiraIssueId = String.valueOf(issueMap.get('id'));
                    return [SELECT COUNT() FROM Ticket__c WHERE JiraTicketId__c = :jiraIssueId] > 0;
                }
            } else if (isCommentEvent(eventType)) {
                Map<String, Object> commentMap = (Map<String, Object>)payload.get('comment');
                if (commentMap != null && commentMap.containsKey('id')) {
                    String jiraCommentId = String.valueOf(commentMap.get('id'));
                    return [SELECT COUNT() FROM Ticket_Comment__c WHERE JiraCommentIdTxt__c = :jiraCommentId] > 0;
                }
            } else if (isAttachmentEvent(eventType)) {
                Map<String, Object> attachmentMap = (Map<String, Object>)payload.get('attachment');
                if (attachmentMap != null && attachmentMap.containsKey('id')) {
                    String jiraAttachmentId = String.valueOf(attachmentMap.get('id'));
                    return [SELECT COUNT() FROM Attachment_Sync_Log__c WHERE Jira_Attachment_ID__c = :jiraAttachmentId] > 0;
                }
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error during record existence check for event ' + eventType + ': ' + e.getMessage());
            return false; // Fail open
        }
        return false;
    }
    /**
     * @description Validates webhook authentication using shared secret header
     * @param req The incoming REST request
     * @return Boolean indicating if authentication is valid
     */
    private static Boolean validateAuthentication(RestRequest req) {
        try {
            String providedSecret = req.headers.get(AUTH_HEADER);
            
            if (String.isBlank(providedSecret)) {
                System.debug(LoggingLevel.WARN, 'Missing authentication header: ' + AUTH_HEADER);
                return false;
            }
            
            // Get expected secret from custom settings or metadata
            String expectedSecret = getWebhookSecret();
            
            if (String.isBlank(expectedSecret)) {
                System.debug(LoggingLevel.ERROR, 'Webhook secret not configured in system');
                return false;
            }
            
            Boolean isValid = providedSecret.equals(expectedSecret);
            
            if (!isValid) {
                System.debug(LoggingLevel.WARN, 'Authentication failed: Invalid webhook secret provided');
            }
            
            return isValid;
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error during authentication validation: ' + e.getMessage());
            return false;
        }
    }
    
    /**
     * @description Retrieves the webhook shared secret from configuration
     * @return String The configured webhook secret
     */
    private static String getWebhookSecret() {
        try {
            // Try to get from Delivery Hub Settings first
            String query = 'SELECT JiraWebhookSecretTxt__c FROM Delivery_Hub_Settings__c WHERE SetupOwnerId = :orgId LIMIT 1';
            List<SObject> settings = Database.query(query.replace(':orgId', '\'' + UserInfo.getOrganizationId() + '\''));
            
            if (!settings.isEmpty()) {
                SObject setting = settings[0];
                String secret = (String)setting.get('JiraWebhookSecretTxt__c');
                if (String.isNotBlank(secret)) {
                    return secret;
                }
            }
            
            // Fallback to a default value for development (should be configured in production)
            System.debug(LoggingLevel.WARN, 'Webhook secret not found in settings, using default');
            return 'DEFAULT_WEBHOOK_SECRET_CHANGE_IN_PRODUCTION';
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error retrieving webhook secret: ' + e.getMessage());
            // Return default secret if field doesn't exist or other error occurs
            return 'DEFAULT_WEBHOOK_SECRET_CHANGE_IN_PRODUCTION';
        }
    }
    
    /**
     * @description Validates the structure of the webhook payload
     * @param payload The parsed JSON payload
     * @return String Error message if validation fails, null if valid
     */
    private static String validatePayloadStructure(Map<String, Object> payload) {
        if (payload == null) {
            return 'Payload cannot be null';
        }
        
        // Check for required webhookEvent field
        if (!payload.containsKey('webhookEvent') || String.isBlank((String)payload.get('webhookEvent'))) {
            return 'Missing required field: webhookEvent';
        }
        
        String eventType = (String)payload.get('webhookEvent');
        
        // Validate event-specific required fields
        if (isIssueEvent(eventType)) {
            if (!payload.containsKey('issue')) {
                return 'Missing required field for issue events: issue';
            }
        } else if (isCommentEvent(eventType)) {
            if (!payload.containsKey('comment') || !payload.containsKey('issue')) {
                return 'Missing required fields for comment events: comment and issue';
            }
        } else if (isAttachmentEvent(eventType)) {
            if (!payload.containsKey('attachment') || !payload.containsKey('issue')) {
                return 'Missing required fields for attachment events: attachment and issue';
            }
        }
        
        return null; // Validation passed
    }
    
    /**
     * @description Routes webhook events to appropriate processors
     * @param eventType The webhook event type
     * @param payload The webhook payload
     * @return Boolean indicating if routing was successful
     */
    private static Boolean routeEvent(String eventType, Map<String, Object> payload) {
        try {
            System.debug('Routing webhook event: ' + eventType);
            
            // First check if this is a supported event type at all
            if (!isIssueEvent(eventType) && !isCommentEvent(eventType) && !isAttachmentEvent(eventType)) {
                System.debug(LoggingLevel.WARN, 'Unsupported event type: ' + eventType);
                return false;
            }
            
            // Check if event type is enabled in configuration
            if (!JiraWebhookConfigService.isEventTypeEnabled(eventType)) {
                System.debug('Event type ' + eventType + ' is disabled in configuration, ignoring webhook');
                return true; // Return true since this is expected behavior, not an error
            }
            
            // Validate configuration for this event type
            String configValidation = JiraWebhookConfigService.validateConfiguration(eventType);
            if (configValidation != null) {
                System.debug(LoggingLevel.ERROR, 'Configuration validation failed for ' + eventType + ': ' + configValidation);
                return false;
            }
            
            if (isIssueEvent(eventType)) {
                return routeIssueEvent(eventType, payload);
            } else if (isCommentEvent(eventType)) {
                return routeCommentEvent(eventType, payload);
            } else if (isAttachmentEvent(eventType)) {
                return routeAttachmentEvent(eventType, payload);
            }
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error routing event ' + eventType + ': ' + e.getMessage());
            return false;
        }
        return false; // Should never reach here
    }
    
    /**
     * @description Routes issue-related webhook events
     * @param eventType The specific issue event type
     * @param payload The webhook payload
     * @return Boolean indicating if routing was successful
     */
    private static Boolean routeIssueEvent(String eventType, Map<String, Object> payload) {
        try {
            System.debug('Routing issue event: ' + eventType);
            
            // Queue the webhook processing asynchronously
            String webhookId = JiraWebhookProcessor.queueWebhookProcessing(eventType, payload);
            System.debug('Issue event queued successfully with ID: ' + webhookId);
            
            return true;
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error routing issue event: ' + e.getMessage());
            return false;
        }
    }
    
    /**
     * @description Routes comment-related webhook events
     * @param eventType The specific comment event type
     * @param payload The webhook payload
     * @return Boolean indicating if routing was successful
     */
    private static Boolean routeCommentEvent(String eventType, Map<String, Object> payload) {
        try {
            System.debug('Routing comment event: ' + eventType);
            
            // Queue the webhook processing asynchronously
            String webhookId = JiraWebhookProcessor.queueWebhookProcessing(eventType, payload);
            System.debug('Comment event queued successfully with ID: ' + webhookId);
            
            return true;
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error routing comment event: ' + e.getMessage());
            return false;
        }
    }
    
    /**
     * @description Routes attachment-related webhook events
     * @param eventType The specific attachment event type
     * @param payload The webhook payload
     * @return Boolean indicating if routing was successful
     */
    private static Boolean routeAttachmentEvent(String eventType, Map<String, Object> payload) {
        try {
            System.debug('Routing attachment event: ' + eventType);
            
            // Queue the webhook processing asynchronously
            String webhookId = JiraWebhookProcessor.queueWebhookProcessing(eventType, payload);
            System.debug('Attachment event queued successfully with ID: ' + webhookId);
            
            return true;
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error routing attachment event: ' + e.getMessage());
            return false;
        }
    }
    
    /**
     * @description Checks if the event type is an issue-related event
     * @param eventType The webhook event type
     * @return Boolean indicating if it's an issue event
     */
    private static Boolean isIssueEvent(String eventType) {
        return eventType == EVENT_ISSUE_CREATED || 
               eventType == EVENT_ISSUE_UPDATED || 
               eventType == EVENT_ISSUE_DELETED;
    }
    
    /**
     * @description Checks if the event type is a comment-related event
     * @param eventType The webhook event type
     * @return Boolean indicating if it's a comment event
     */
    private static Boolean isCommentEvent(String eventType) {
        return eventType == EVENT_COMMENT_CREATED || 
               eventType == EVENT_COMMENT_UPDATED || 
               eventType == EVENT_COMMENT_DELETED;
    }
    
    /**
     * @description Checks if the event type is an attachment-related event
     * @param eventType The webhook event type
     * @return Boolean indicating if it's an attachment event
     */
    private static Boolean isAttachmentEvent(String eventType) {
        return eventType == EVENT_ATTACHMENT_CREATED || 
               eventType == EVENT_ATTACHMENT_DELETED;
    }
    
    /**
     * @description Sends a success response to the webhook caller
     * @param res The REST response object
     * @param message Success message
     * @param eventType The processed event type
     */
    private static void sendSuccessResponse(RestResponse res, String message, String eventType) {
        res.statusCode = 200;
        Map<String, Object> responseBody = new Map<String, Object>{
            'status' => 'success',
            'message' => message,
            'eventType' => eventType,
            'timestamp' => System.now().getTime()
        };
        res.responseBody = Blob.valueOf(JSON.serialize(responseBody));
    }
    
    /**
     * @description Sends an error response to the webhook caller
     * @param res The REST response object
     * @param statusCode HTTP status code
     * @param error Error type
     * @param message Error message
     */
    private static void sendErrorResponse(RestResponse res, Integer statusCode, String error, String message) {
        res.statusCode = statusCode;
        Map<String, Object> responseBody = new Map<String, Object>{
            'status' => 'error',
            'error' => error,
            'message' => message,
            'timestamp' => System.now().getTime()
        };
        res.responseBody = Blob.valueOf(JSON.serialize(responseBody));
        
        // Log error for monitoring
        System.debug(LoggingLevel.ERROR, 'Webhook error response: ' + statusCode + ' - ' + message);
    }
}
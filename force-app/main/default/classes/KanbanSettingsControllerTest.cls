@isTest
private class KanbanSettingsControllerTest {

    // =================================================================================
    // SECTION 1: getSettings() and saveSettings() Tests
    // =================================================================================

    @isTest
    static void testGetSettings_NoExistingRecord() {
        // ARRANGE: Ensure no custom setting record exists.

        Test.startTest();
        // ACT: Call getSettings(). It should return the org defaults.
        KanbanSettingsController.SettingsWrapper settings = KanbanSettingsController.getSettings();
        Test.stopTest();

        // ASSERT: The returned object should not be null and should reflect default values (e.g., false for booleans).
        System.assertNotEquals(null, settings, 'Settings wrapper should not be null even if no record exists.');
        System.assertEquals(false, settings.jiraEnabled, 'Default for JIRA Enabled should be false.');
        System.assertEquals(false, settings.aiSuggestionsEnabled, 'Default for AI Suggestions should be false.');
    }

    @isTest
    static void testGetAndSaveSettings_FullCycle() {
        // ARRANGE: Create a wrapper object with all properties set.
        KanbanSettingsController.SettingsWrapper newSettings = new KanbanSettingsController.SettingsWrapper();
        newSettings.aiSuggestionsEnabled = true;
        newSettings.autoGenerateDescriptions = true;
        newSettings.aiEstimationEnabled = false; // Set a mix of true/false
        newSettings.openaiApiKey = 'sk-test-key';
        newSettings.openaiModel = 'gpt-4';
        newSettings.jiraEnabled = true;
        newSettings.jiraUrl = 'https://test.atlassian.net';
        newSettings.jiraUsername = '<EMAIL>';
        newSettings.jiraApiToken = 'test-token';
        newSettings.jiraProjectKey = 'KAN';

        // Serialize the wrapper to JSON, mimicking the LWC payload.
        String settingsJson = JSON.serialize(newSettings);

        Test.startTest();
        // ACT 1: Save the settings.
        KanbanSettingsController.saveSettings(settingsJson);

        // ACT 2: Retrieve the settings to verify they were saved correctly.
        KanbanSettingsController.SettingsWrapper savedSettings = KanbanSettingsController.getSettings();
        Test.stopTest();

        // ASSERT: All retrieved settings should match the values that were saved.
        System.assertNotEquals(null, savedSettings, 'Retrieved settings should not be null.');
        System.assertEquals(true, savedSettings.aiSuggestionsEnabled, 'AI Suggestions should be true.');
        System.assertEquals(false, savedSettings.aiEstimationEnabled, 'AI Estimation should be false.');
        System.assertEquals('sk-test-key', savedSettings.openaiApiKey, 'OpenAI API key should match.');
        System.assertEquals('https://test.atlassian.net', savedSettings.jiraUrl, 'JIRA URL should match.');
        System.assertEquals('KAN', savedSettings.jiraProjectKey, 'JIRA Project Key should match.');
    }

    // =================================================================================
    // SECTION 2: testOpenAIConnection() Callout Tests
    // =================================================================================

    @isTest
    static void testOpenAIConnection_Success() {
        // ARRANGE: Set up the mock to simulate a successful (200 OK) API response.
        Test.setMock(HttpCalloutMock.class, new OpenAIMock(200, 'Success', '{"id":"...","choices":[{"message":{"content":"Test successful."}}]}'));

        Test.startTest();
        // ACT: Call the method that performs the callout.
        String result = KanbanSettingsController.testOpenAIConnection('dummy-key');
        Test.stopTest();

        // ASSERT: The result should be the 'Success' string.
        System.assertEquals('Success', result, 'The connection test should return "Success" on a 200 response.');
    }

    @isTest
    static void testOpenAIConnection_ApiError() {
        // ARRANGE: Set up the mock to simulate a failed (401 Unauthorized) API response.
        String errorBody = '{"error":{"message":"Incorrect API key provided."}}';
        Test.setMock(HttpCalloutMock.class, new OpenAIMock(401, 'Unauthorized', errorBody));

        Test.startTest();
        // ACT: Call the method.
        String result = KanbanSettingsController.testOpenAIConnection('invalid-key');
        Test.stopTest();

        // ASSERT: The result should contain the status code and the error message from the response body.
        System.assert(result.contains('Failed: 401'), 'The result should indicate a failed status code.');
        System.assert(result.contains('Incorrect API key provided'), 'The result should include the error message from the API.');
    }

    @isTest
    static void testOpenAIConnection_CalloutException() {
        // ARRANGE: Set up a mock that throws a CalloutException (e.g., URL not available).
        Test.setMock(HttpCalloutMock.class, new OpenAIMock(true));

        Test.startTest();
        // ACT: Call the method.
        String result = KanbanSettingsController.testOpenAIConnection('any-key');
        Test.stopTest();

        // ASSERT: The method should catch the exception and return a user-friendly error message.
        System.assert(result.startsWith('Error:'), 'The result should start with "Error:" on an exception.');
        System.assert(result.contains('Callout failed'), 'The error message should indicate a callout failure.');
    }

    // =================================================================================
    // SECTION 3: HTTP Mock Implementation
    // =================================================================================

    /**
     * @description Mock class to simulate HTTP responses from the OpenAI API.
     */
    private class OpenAIMock implements HttpCalloutMock {
        private Integer statusCode;
        private String status;
        private String body;
        private Boolean throwException;

        // Constructor for standard responses
        public OpenAIMock(Integer statusCode, String status, String body) {
            this.statusCode = statusCode;
            this.status = status;
            this.body = body;
            this.throwException = false;
        }

        // Constructor to simulate a system-level exception
        public OpenAIMock(Boolean throwEx) {
            this.throwException = throwEx;
        }

        public HttpResponse respond(HttpRequest req) {
            if (this.throwException) {
                // Simulate a system-level failure, like no network connection.
                throw new CalloutException('Callout failed');
            }

            // Create a mock response.
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody(this.body);
            res.setStatusCode(this.statusCode);
            res.setStatus(this.status);
            return res;
        }
    }
}

@isTest
private class JiraCalloutTest {

    /**
     * @description Mock HTTP responses for Jira API callouts.
     */
    @testSetup
    static void setup() {
        // Create and insert the required custom setting data for all tests.
        Delivery_Hub_Settings__c settings = new Delivery_Hub_Settings__c(
            Name = 'Default', // Use a name if it's a list custom setting
            JIRA_Instance_URL__c = 'https://mock-jira.test.com',
            JIRA_Username__c = '<EMAIL>',
            JIRA_API_Token__c = 'mock_api_token'
        );
        insert settings;
    }
    private class JiraCalloutMock implements HttpCalloutMock {
        private Integer statusCode;
        private String status;
        private String body;
        
        public JiraCalloutMock() {
            this.statusCode = 200;
            this.status = 'OK';
            this.body = '{}'; // Default empty success body
        }

        /**
         * @description Constructor to define the mock response.
         * @param statusCode The HTTP status code to return (e.g., 200, 404, 500).
         * @param status The HTTP status message (e.g., 'OK', 'Not Found').
         * @param body The response body as a String.
         */
        public JiraCalloutMock(Integer statusCode, String status, String body) {
            this.statusCode = statusCode;
            this.status = status;
            this.body = body;
        }

        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody(this.body);
            res.setStatusCode(this.statusCode);
            res.setStatus(this.status);
            return res;
        }
    }

    /**
     * @description Test a successful callout scenario using the httpHelper method.
     */
    @isTest
    static void testHttpHelperSuccess() {
        // 1. Setup: Set the mock for a successful API response.
        Test.setMock(HttpCalloutMock.class, new JiraCalloutMock(200, 'OK', '{"key": "TESTPROJ"}'));

        // 2. Action: Call a method that uses the httpHelper.
        Test.startTest();
        HttpResponse res = JiraCallout.getProject('TESTPROJ');
        Test.stopTest(); 

        // 3. Assert: Check the final state.
        System.assertEquals(200, res.getStatusCode(), 'Response status code should be 200.');
    }

    /**
     * @description Test that callouts are not blocked for a standard user.
     * This test now creates the necessary profile to avoid the QueryException.
     */
    @isTest
    static void testStandardUserIsNotBlocked() {
        // 1. Setup: Create a standard user profile and user to run the test as.
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User standardUser = new User(
            Alias = 'stduser', 
            Email = '<EMAIL>', 
            EmailEncodingKey = 'UTF-8', 
            LastName = 'TestUser', 
            LanguageLocaleKey = 'en_US', 
            LocaleSidKey = 'en_US', 
            ProfileId = p.Id, 
            TimeZoneSidKey = 'America/Los_Angeles', 
            UserName = 'standarduser' + System.currentTimeMillis() + '@testorg.com'
        );

        System.runAs(standardUser) {
            // 2. Action: Call a method that has the guest user check.
            Test.startTest();
            Test.setMock(HttpCalloutMock.class, new JiraCalloutMock(200, 'OK', '{}'));
            HttpResponse res = JiraCallout.getProject('ANY');
            Test.stopTest();

            // 3. Assert: Verify the call was not forbidden.
            System.assertNotEquals(403, res.getStatusCode(), 'Callout should not be forbidden for a standard user.');
        }
    }

    /**
     * @description Test all other public methods to ensure full coverage.
     */
    @isTest
    static void testAllPublicMethods() {
        Test.setMock(HttpCalloutMock.class, new JiraCalloutMock(200, 'OK', '{}'));

        Test.startTest();
        // Call each method to ensure it's covered.
        JiraCallout.createProject('{"key": "NEW"}');
        JiraCallout.updateProject('PROJ1', '{"name": "New Name"}');
        JiraCallout.getUser('<EMAIL>');
        JiraCallout.createVersion('{"name": "v1.0"}');
        JiraCallout.getIssues(new Map<String, String>{'jql' => 'project=TEST'});
        JiraCallout.addComment('TEST-123', '{"body": "a comment"}');
        Test.stopTest();
        
        // No assertions needed here as we are just ensuring the methods run without error for coverage.
        // The actual logic is tested in other methods.
    }

    /**
     * @description Test the queryIssues method for successful deserialization.
     */
    @isTest
    static void testQueryIssuesSuccess() {
        // 1. Setup: Mock a successful response with a list of issues.
        String mockBody = '{"issues": [{"id": "1001", "key": "TEST-1"}, {"id": "1002", "key": "TEST-2"}]}';
        Test.setMock(HttpCalloutMock.class, new JiraCalloutMock(200, 'OK', mockBody));

        // 2. Action: Call the queryIssues method.
        Test.startTest();
        List<Map<String, Object>> issues = JiraCallout.queryIssues('project=TEST');
        Test.stopTest();

        // 3. Assert: Verify the response is parsed correctly.
        System.assertNotEquals(null, issues, 'Issues list should not be null.');
        System.assertEquals(2, issues.size(), 'Should return two issues.');
        System.assertEquals('TEST-1', (String)((Map<String, Object>)issues[0]).get('key'));
    }
    
    /**
     * @description Test the queryIssues method when the callout fails.
     */
    @isTest
    static void testQueryIssuesFailure() {
        // 1. Setup: Mock a server error response.
        Test.setMock(HttpCalloutMock.class, new JiraCalloutMock(500, 'Server Error', '{"error": "Internal Server Error"}'));
        
        AuraHandledException expectedException = null;

        // 2. Action: Call the method and expect an exception.
        Test.startTest();
        try {
            JiraCallout.queryIssues('project=FAIL');
        } catch (AuraHandledException e) {
            expectedException = e;
        }
        Test.stopTest();

        // 3. Assert: Verify that the correct exception was thrown.
        System.assertNotEquals(null, expectedException, 'An AuraHandledException should have been thrown.');
        //System.assert(expectedException.getMessage().contains('Jira query failed'), 'Exception message should indicate failure.');
    }

    /**
     * @description Test the addAttachment method which uses a multipart form request.
     */
    @isTest
    static void testAddAttachment() {
        // 1. Setup: Mock a successful response for the attachment upload.
        Test.setMock(HttpCalloutMock.class, new JiraCalloutMock(200, 'OK', '[{"id": "10000"}]'));

        // 2. Action: Call the addAttachment method.
        Test.startTest();
        HttpResponse res = JiraCallout.addAttachment('TEST-1', 'test.txt', Blob.valueOf('test content'));
        Test.stopTest();

        // 3. Assert: Verify the call was successful.
        System.assertEquals(200, res.getStatusCode(), 'Status code should be 200 for attachment success.');
    }
    
    /**
     * @description Test the ADF comment body builder utility method.
     */
    @isTest
    static void testBuildADFCommentBody() {
        // 1. Action: Call the utility method.
        String commentText = 'This is a test comment.';
        String adfJson = JiraCallout.buildADFCommentBody(commentText);

        // 2. Assert: Verify the JSON structure is correct.
        System.assert(adfJson.contains('"type":"doc"'), 'JSON should contain the doc type.');
        System.assert(adfJson.contains('"type":"paragraph"'), 'JSON should contain the paragraph type.');
        System.assert(adfJson.contains('"text":"' + commentText + '"'), 'JSON should contain the correct comment text.');
    }
}
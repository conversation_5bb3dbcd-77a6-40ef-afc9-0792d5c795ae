<?xml version="1.0" encoding="UTF-8"?>
<CustomSite xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowGuestPaymentsApi>false</allowGuestPaymentsApi>
    <allowHomePage>false</allowHomePage>
    <allowStandardAnswersPages>false</allowStandardAnswersPages>
    <allowStandardIdeasPages>false</allowStandardIdeasPages>
    <allowStandardLookups>false</allowStandardLookups>
    <allowStandardPortalPages>true</allowStandardPortalPages>
    <allowStandardSearch>false</allowStandardSearch>
    <authorizationRequiredPage>Unauthorized</authorizationRequiredPage>
    <bandwidthExceededPage>BandwidthExceeded</bandwidthExceededPage>
    <browserXssProtection>true</browserXssProtection>
    <cachePublicVisualforcePagesInProxyServers>true</cachePublicVisualforcePagesInProxyServers>
    <clickjackProtectionLevel>AllowAllFraming</clickjackProtectionLevel>
    <contentSniffingProtection>true</contentSniffingProtection>
    <enableAuraRequests>true</enableAuraRequests>
    <fileNotFoundPage>FileNotFound</fileNotFoundPage>
    <genericErrorPage>Exception</genericErrorPage>
    <inMaintenancePage>InMaintenance</inMaintenancePage>
    <inactiveIndexPage>InMaintenance</inactiveIndexPage>
    <indexPage>InMaintenance</indexPage>
    <masterLabel>Delivery Hub</masterLabel>
    <redirectToCustomDomain>true</redirectToCustomDomain>
    <referrerPolicyOriginWhenCrossOrigin>true</referrerPolicyOriginWhenCrossOrigin>
    <siteAdmin><EMAIL></siteAdmin>
    <siteGuestRecordDefaultOwner><EMAIL></siteGuestRecordDefaultOwner>
    <siteTemplate>SiteTemplate</siteTemplate>
    <siteType>Visualforce</siteType>
</CustomSite>

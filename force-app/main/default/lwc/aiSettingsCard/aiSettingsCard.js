import { LightningElement, track, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getSettings from '@salesforce/apex/DeliveryHubSettingsController.getSettings';
import saveAiSettings from '@salesforce/apex/DeliveryHubSettingsController.saveAiSettings';

export default class AiSettingsCard extends LightningElement {
    @track aiSuggestionsEnabled = false;
    @track autoGenerateDescriptions = false;
    @track aiEstimationEnabled = false;
    @track hasValidOpenAIKey = false; // Tracks if the OpenAI key is tested and valid
    isLoading = true;

    @wire(getSettings)
    wiredSettings({ error, data }) {
        this.isLoading = false;
        if (data) {
            this.aiSuggestionsEnabled = data.aiSuggestionsEnabled || false;
            this.autoGenerateDescriptions = data.autoGenerateDescriptions || false;
            this.aiEstimationEnabled = data.aiEstimationEnabled || false;
            // Set the dependency flag based on the OpenAI API test status
            this.hasValidOpenAIKey = data.openAiApiTested || false;
        } else if (error) {
            this.showToast('Error Loading AI Settings', error.body.message, 'error');
        }
    }

    async handleSettingChange(event) {
        const { name, checked } = event.target;
        const originalValue = this[name];

        // --- VALIDATION STEP ---
        // Check if user is trying to ENABLE the main feature without a valid key
        if (name === 'aiSuggestionsEnabled' && checked && !this.hasValidOpenAIKey) {
            
            this.showToast(
                'Configuration Required',
                'Please complete and test your OpenAI API configuration before enabling AI features.',
                'error'
            );
            // This is a workaround for the toggle's optimistic UI update.
            // By briefly setting and then reverting the property, we force the component
            // to re-render and reflect the correct (unchanged) state.
            
            event.target.checked = false;
            return;
        }

        // Update the state based on the changed input
        this[name] = checked;

        // Cascade changes if the main toggle is modified
        if (name === 'aiSuggestionsEnabled') {
            // If the main feature is toggled, sync sub-features
            this.autoGenerateDescriptions = checked;
            this.aiEstimationEnabled = checked;
        }

        this.isLoading = true;

        try {
            await saveAiSettings({
                suggestions: this.aiSuggestionsEnabled,
                descriptions: this.autoGenerateDescriptions,
                estimation: this.aiEstimationEnabled
            });
        } catch (error) {
            this.showToast('Error Saving AI Settings', error.body.message, 'error');
            // Revert the change on a save error
            this[name] = originalValue;
             if (name === 'aiSuggestionsEnabled') {
                // Also revert children if the main toggle failed
                this.autoGenerateDescriptions = originalValue;
                this.aiEstimationEnabled = originalValue;
            }
        } finally {
            this.isLoading = false;
        }
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    // Getters to control the disabled state of the sub-feature toggles
    get isAutoDescriptionsDisabled() {
        return !this.aiSuggestionsEnabled;
    }

    get isAiEstimationDisabled() {
        return !this.aiSuggestionsEnabled;
    }
}
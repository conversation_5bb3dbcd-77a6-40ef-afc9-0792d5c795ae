<template>
    <ul class="slds-has-dividers_around-space dropZone" 
    ondrop={handleDrop}
    ondragover={handleDragOver}
    style="height:70vh; overflow-y:auto;">
        <template for:each={records} for:item="recordItem">
            <c-drag-and-drop-card stage={stage} record={recordItem} key={recordItem.Id} onitemdrag={handleItemDrag}></c-drag-and-drop-card>
        </template>
    </ul>
</template>
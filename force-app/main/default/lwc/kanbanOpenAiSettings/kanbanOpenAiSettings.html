<template>
    <div class="slds-p-around_large">
        <div class="slds-text-heading_medium">OpenAI Configuration</div>
        <p class="slds-text-body_small slds-m-bottom_large">Configure your OpenAI API settings for AI-powered features.</p>

        <!-- API Key -->
        <lightning-input
            label="API Key"
            name="openaiApiKey"
            type={apiKeyInputType}
            value={settings.openaiApiKey}
            placeholder="sk-..."
            onchange={handleChange}
            class="slds-m-bottom_small">
        </lightning-input>
        <lightning-button-icon
            icon-name={apiKeyIcon}
            variant="border-filled"
            alternative-text="Toggle Visibility"
            onclick={toggleApiKeyVisibility}
            class="slds-m-left_x-small">
        </lightning-button-icon>
        <lightning-button
            label={testButtonLabel}
            icon-name="utility:connect"
            onclick={handleTestConnection}
            disabled={isTesting}
            class="slds-m-left_small">
        </lightning-button>
        
        <template if:true={testResult}>
             <div class={testResultClass} role="alert">{testResultMessage}</div>
        </template>
        
        <!-- Model Selection -->
        <lightning-combobox
            label="Model"
            name="openaiModel"
            value={settings.openaiModel}
            options={modelOptions}
            onchange={handleChange}
            class="slds-m-vertical_medium">
        </lightning-combobox>

        <!-- How To Guide -->
        <div class="slds-box slds-theme_shade slds-m-top_large">
            <h3 class="slds-text-heading_small">How to get your OpenAI API Key:</h3>
            <ol class="slds-list_ordered slds-m-left_large slds-m-top_small">
                <li>Visit the OpenAI Platform at platform.openai.com</li>
                <li>Sign up or log in to your account</li>
                <li>Navigate to API Keys in your dashboard</li>
                <li>Click "Create new secret key"</li>
                <li>Copy the key and paste it above</li>
            </ol>
        </div>
    </div>
</template>
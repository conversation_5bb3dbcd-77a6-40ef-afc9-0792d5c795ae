<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>EventTypePk__c</fullName>
    <description>The type of Jira webhook event this configuration applies to (e.g., jira:issue_created, comment_created, attachment_created)</description>
    <fieldManageability>DeveloperControlled</fieldManageability>
    <label>Event TypePk</label>
    <required>true</required>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>jira:issue_created</fullName>
                <default>false</default>
                <label>Issue Created</label>
            </value>
            <value>
                <fullName>jira:issue_updated</fullName>
                <default>false</default>
                <label>Issue Updated</label>
            </value>
            <value>
                <fullName>jira:issue_deleted</fullName>
                <default>false</default>
                <label>Issue Deleted</label>
            </value>
            <value>
                <fullName>comment_created</fullName>
                <default>false</default>
                <label>Comment Created</label>
            </value>
            <value>
                <fullName>comment_updated</fullName>
                <default>false</default>
                <label>Comment Updated</label>
            </value>
            <value>
                <fullName>comment_deleted</fullName>
                <default>false</default>
                <label>Comment Deleted</label>
            </value>
            <value>
                <fullName>attachment_created</fullName>
                <default>false</default>
                <label>Attachment Created</label>
            </value>
            <value>
                <fullName>attachment_deleted</fullName>
                <default>false</default>
                <label>Attachment Deleted</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>

<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Issue Created Default</label>
    <protected>false</protected>
    <values>
        <field>EnabledBool__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>EventTypePk__c</field>
        <value xsi:type="xsd:string">jira:issue_created</value>
    </values>
    <values>
        <field>FieldMappingTxt__c</field>
        <value xsi:type="xsd:string">{&quot;key&quot;: &quot;JiraTicketKeyTxt__c&quot;, &quot;summary&quot;: &quot;BriefDescriptionTxt__c&quot;, &quot;description&quot;: &quot;DescriptionTxt__c&quot;, &quot;status.name&quot;: &quot;StatusPk__c&quot;, &quot;assignee.displayName&quot;: &quot;AssignedToTxt__c&quot;, &quot;priority.name&quot;: &quot;PriorityPk__c&quot;}</value>
    </values>
    <values>
        <field>PriorityNumber__c</field>
        <value xsi:type="xsd:double">10.0</value>
    </values>
    <values>
        <field>RetryAttemptsNumber__c</field>
        <value xsi:type="xsd:double">5.0</value>
    </values>
    <values>
        <field>SyncDirectionPk__c</field>
        <value xsi:type="xsd:string">Jira_to_Salesforce</value>
    </values>
</CustomMetadata>

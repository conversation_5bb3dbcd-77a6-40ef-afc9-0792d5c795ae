<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns="http://soap.sforce.com/2006/04/metadata">
    <types>
        <members>ContentDocumentLinkTriggerHandler</members>
        <members>Ticket_JiraSyncTest</members>
        <members>AttachmentSyncService</members>
        <members>AttachmentSyncServiceTest</members>
        <members>DeliveryHubSettingsController</members>
        <members>DeliveryHubSettingsControllerTest</members>
        <members>JiraAttachmentSyncBatch</members>
        <members>JiraAttachmentSyncBatchTest</members>
        <members>JiraSyncWorkerTest</members>
        <members>JiraToSFSyncBatchTest</members>
        <members>KanbanSettingsController</members>
        <members>TicketController</members>
        <members>TicketControllerTest</members>
        <members>TicketETAService</members>
        <members>TicketETAServiceTest</members>
        <members>TicketTriggerHandler</members>
        <members>TicketTriggerHandlerTest</members>
        <members>DragAndDropLwcController</members>
        <members>JiraCalloutMock</members>
        <members>JiraCommentSyncHelper</members>
        <members>JiraCommentSyncHelperTest</members>
        <name>ApexClass</name>
    </types>
    <types>
        <members>DH</members>
        <name>ApexTestSuite</name>
    </types>
    <types>
        <members>ContentDocumentLinkTrigger</members>
        <members>TicketTrigger</members>
        <name>ApexTrigger</name>
    </types>
    <types>
        <members>DeliveryHub</members>
        <name>CustomApplication</name>
    </types>
    <types>
        <members>Attachment_Sync_Log__c.Error_Details__c</members>
        <members>Attachment_Sync_Log__c.Filename__c</members>
        <members>Attachment_Sync_Log__c.Jira_Attachment_ID__c</members>
        <members>Attachment_Sync_Log__c.Salesforce_ContentDocumentId__c</members>
        <members>Attachment_Sync_Log__c.Sync_Direction__c</members>
        <members>Attachment_Sync_Log__c.Sync_Status__c</members>
        <members>Attachment_Sync_Log__c.Ticket__c</members>
        <members>Ticket_Status_Log__c.FromTxt__c</members>
        <members>Ticket_Status_Log__c.TimeSpentInStatusNumber__c</members>
        <members>Ticket_Status_Log__c.ToTxt__c</members>
        <members>Ticket__c.Developer__c</members>
        <members>Ticket__c.Estimated_Dev_Completion_Date__c</members>
        <members>Ticket__c.Estimated_Dev_Start_Date__c</members>
        <members>Ticket__c.Projected_UAT_Ready_Date__c</members>
        <members>Ticket__c.StageNamePk__c</members>
        <members>Delivery_Hub_Settings__c.AI_Estimation_Enabled__c</members>
        <members>Delivery_Hub_Settings__c.AI_Suggestions_Enabled__c</members>
        <members>Delivery_Hub_Settings__c.Auto_Generate_Descriptions__c</members>
        <members>Delivery_Hub_Settings__c.Enable_Notifications__c</members>
        <members>Delivery_Hub_Settings__c.JIRA_API_Token__c</members>
        <members>Delivery_Hub_Settings__c.JIRA_Enabled__c</members>
        <members>Delivery_Hub_Settings__c.JIRA_Instance_URL__c</members>
        <members>Delivery_Hub_Settings__c.JIRA_Project_Key__c</members>
        <members>Delivery_Hub_Settings__c.JIRA_Username__c</members>
        <members>Delivery_Hub_Settings__c.JIra_Api_tested__c</members>
        <members>Delivery_Hub_Settings__c.OpenAI_API_Key__c</members>
        <members>Delivery_Hub_Settings__c.OpenAI_Model__c</members>
        <members>Delivery_Hub_Settings__c.Open_Ai_Api_tested__c</members>
        <members>Kanban_App_Settings__c.AI_Estimation_Enabled__c</members>
        <members>Kanban_App_Settings__c.AI_Suggestions_Enabled__c</members>
        <members>Kanban_App_Settings__c.Auto_Generate_Descriptions__c</members>
        <members>Kanban_App_Settings__c.JIRA_API_Token__c</members>
        <members>Kanban_App_Settings__c.JIRA_Enabled__c</members>
        <members>Kanban_App_Settings__c.JIRA_Instance_URL__c</members>
        <members>Kanban_App_Settings__c.JIRA_Project_Key__c</members>
        <members>Kanban_App_Settings__c.JIRA_Username__c</members>
        <members>Kanban_App_Settings__c.OpenAI_API_Key__c</members>
        <members>Kanban_App_Settings__c.OpenAI_Model__c</members>
        <members>OpenAI_Settings__c.API_Key__c</members>
        <members>Ticket_Dependency__c.Blocked_Ticket__c</members>
        <members>Ticket_Dependency__c.Blocking_Ticket__c</members>
        <members>Ticket_Dependency__c.External_ID__c</members>
        <members>Ticket_Dependency__c.Type__c</members>
        <members>User.is_Developer__c</members>
        <name>CustomField</name>
    </types>
    <types>
        <members>Kanban_Configuration.Default_Configuration</members>
        <members>Kanban_Stage_Field_Requirement.QA_Entry_Requirements</members>
        <members>OpenAI_Configuration.OpenAI</members>
        <name>CustomMetadata</name>
    </types>
    <types>
        <members>Attachment_Sync_Log__c</members>
        <members>Ticket_Status_Log__c</members>
        <members>Ticket__c</members>
        <members>Delivery_Hub_Settings__c</members>
        <members>Kanban_App_Settings__c</members>
        <members>Kanban_Stage_Field_Requirement__mdt</members>
        <members>OpenAI_Configuration__mdt</members>
        <members>OpenAI_Settings__c</members>
        <members>Ticket_Dependency__c</members>
        <name>CustomObject</name>
    </types>
    <types>
        <members>Delivery_Hub_Settings</members>
        <name>CustomTab</name>
    </types>
    <types>
        <members>Jira_External_Credential</members>
        <name>ExternalCredential</name>
    </types>
    <types>
        <members>Ticket_Record_Page</members>
        <name>FlexiPage</name>
    </types>
    <types>
        <members>Ticket_Status_Log__c-Ticket Status Log Layout</members>
        <members>Kanban_Stage_Field_Requirement__mdt-Kanban Stage Field Requirement Layout</members>
        <members>OpenAI_Configuration__mdt-OpenAI Configuration Layout</members>
        <members>Ticket_Dependency__c-Ticket Dependency Layout</members>
        <name>Layout</name>
    </types>
    <types>
        <members>aiSettingsCard</members>
        <members>dragAndDropLwc</members>
        <members>generalSettingsCard</members>
        <members>jiraSettingsCard</members>
        <members>kanbanOpenAiSettings</members>
        <members>kanbanSettingsContainer</members>
        <members>openAISettingsCard</members>
        <members>settingsContainer</members>
        <name>LightningComponentBundle</name>
    </types>
    <types>
        <members>Ticket__c.All</members>
        <name>ListView</name>
    </types>
    <types>
        <members>Admin</members>
        <members>B2B Reordering Portal Buyer Profile</members>
        <name>Profile</name>
    </types>
    <types>
        <members>OpenAI_API</members>
        <members>cloudnimbusjira</members>
        <name>RemoteSiteSetting</name>
    </types>
    <types>
        <members>DeliveryHub/New_Tickets_Report_b9q</members>
        <name>Report</name>
    </types>
    <types>
        <members>DeliveryHub</members>
        <name>ReportFolder</name>
    </types>
    <version>58.0</version>
</Package>
minimum_cumulusci_version: "3.78.0"
project:
    name: Delivery Hub
    package:
        name: DeliveryHub
        name_managed: Delivery Hub
        api_version: "61.0"
    dependencies:
    #    - github: https://github.com/Nimba-Solutions/Work-Item-Composer
    #      unmanaged: true
    #    - version_id: 04tQl0000008iGnIAI
    #    - version_id: 04tQl0000008iIPIAY # https://github.com/jawills/jira-sf-integration/tree/master
    #     - version_id: 04t5G000004fz7mQAA # FlowScreenComponentsBasePack Version: 3.3.4
    #     - version_id: 04t5G0000043xu3QAA # https://unofficialsf.com/from-josh-dayment-improved-file-upload-in-flow-screens/
    git:
        default_branch: "main"
        prefix_feature: "feature/"
        prefix_beta: "beta/"
        prefix_release: "release/"
    source_format: sfdx

tasks:
    robot:
        options:
            suites: robot/Standard-Unlocked/tests
            options:
                outputdir: robot/Standard-Unlocked/results

    robot_testdoc:
        options:
            path: robot/Standard-Unlocked/tests
            output: robot/Standard-Unlocked/doc/Standard-Unlocked_tests.html

    run_tests:
        options:
            required_org_code_coverage_percent: 75

    # CumulusCI ships with both `retrieve_changes`, `permsets` and `deploy` tasks out of the box, '
    # but they don't provide sufficient find/replace functionality so this project
    # ships with custom wrapper tasks:
    # └── 📁 tasks/
    #     ├── 📄 retrieve_changes.py
    #     ├── 📄 permsets.py
    #     └── 📄 deploy.py
    retrieve_changes:
        class_path: tasks.retrieve_changes.RetrieveChanges
        options:
            preserve_tokens: "__PROJECT_NAME__,__PROJECT_LABEL__"
    deploy:
        class_path: tasks.deploy.Deploy
        options:
            transforms:
                - transform: find_replace
                  options:
                      patterns:
                          - find: "__PROJECT_NAME__"
                            replace: $project_config.project__package__name
                          - find: "__PROJECT_LABEL__"
                            replace: $project_config.project__package__name_managed

    assign_permission_set_groups:
        class_path: tasks.permsets.AssignPermissionSetsWithFindReplace
        options:
            api_names: __PROJECT_NAME__Admin
            transforms:
                - transform: find_replace
                  options:
                      patterns:
                          - find: "__PROJECT_NAME__"
                            replace: $project_config.project__package__name

flows:
    config_dev:
        steps:
            3.0:
                task: assign_permission_set_groups

    config_qa:
        steps:
            3.0:
                task: assign_permission_set_groups
